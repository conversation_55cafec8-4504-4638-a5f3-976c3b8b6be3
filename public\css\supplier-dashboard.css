/* Indah Berkah Abadi - Supplier Dashboard CSS */
/* Independent CSS for supplier dashboard with proper layout and responsive design */

/*
 * Z-INDEX HIERARCHY:
 * - Sidebar navigation: 1000+ (highest priority)
 * - Modal overlays: 500-999 (high priority, below sidebar)
 * - Dropdown elements: 100-499 (medium-high priority)
 * - Interactive elements: 10-99 (low-medium priority)
 * - Hover states and tooltips: 2-9 (low priority)
 * - Main content: 1 (lowest priority)
 */

/* ===== LAYOUT COMPONENTS ===== */

.supplier-dashboard-layout {
    min-height: 100vh;
    background-color: #f8fafc;
    display: flex;
    flex-direction: column;
}

/* ===== SIDEBAR STYLES ===== */

.supplier-dashboard-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 280px;
    background: linear-gradient(180deg, #1e293b 0%, #334155 100%);
    color: white;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.supplier-dashboard-sidebar.supplier-dashboard-sidebar-open {
    transform: translateX(0);
}

.supplier-dashboard-sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;
}

.supplier-dashboard-sidebar-overlay.supplier-dashboard-sidebar-overlay-show {
    opacity: 1;
    visibility: visible;
}

/* Sidebar Header */
.supplier-dashboard-sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.supplier-dashboard-sidebar-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
}

.supplier-dashboard-logo-icon {
    width: 40px;
    height: 40px;
    background: #3b82f6;
    color: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.875rem;
}

.supplier-dashboard-logo-text {
    flex: 1;
}

.supplier-dashboard-logo-title {
    font-weight: 700;
    font-size: 1rem;
    color: white;
    line-height: 1.2;
}

.supplier-dashboard-logo-subtitle {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

/* Sidebar Navigation */
.supplier-dashboard-nav {
    padding: 1rem 0;
    flex: 1;
}

.supplier-dashboard-nav-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s ease-in-out;
    border-left: 3px solid transparent;
}

.supplier-dashboard-nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.supplier-dashboard-nav-item.supplier-dashboard-nav-item-active {
    background: rgba(59, 130, 246, 0.2);
    color: #60a5fa;
    border-left-color: #3b82f6;
}

.supplier-dashboard-nav-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
}

.supplier-dashboard-nav-text {
    flex: 1;
}

/* Sidebar Footer */
.supplier-dashboard-sidebar-footer {
    padding: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.supplier-dashboard-user-menu {
    position: relative;
}

.supplier-dashboard-user-menu-button {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 8px;
    color: white;
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;
}

.supplier-dashboard-user-menu-button:hover {
    background: rgba(255, 255, 255, 0.15);
}

.supplier-dashboard-user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
}

.supplier-dashboard-user-avatar {
    width: 40px;
    height: 40px;
    background: #3b82f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.supplier-dashboard-user-initial {
    font-weight: 600;
    font-size: 0.875rem;
    color: white;
}

.supplier-dashboard-user-details {
    flex: 1;
    text-align: left;
}

.supplier-dashboard-user-name {
    font-weight: 600;
    font-size: 0.875rem;
    color: white;
    margin: 0;
    line-height: 1.2;
}

.supplier-dashboard-user-role {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
}

.supplier-dashboard-dropdown-arrow {
    width: 16px;
    height: 16px;
    color: rgba(255, 255, 255, 0.7);
    transition: transform 0.2s ease-in-out;
}

/* User Menu Dropdown */
.supplier-dashboard-user-menu-dropdown {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    margin-bottom: 0.5rem;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    z-index: 1100;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.2s ease-in-out;
}

.supplier-dashboard-user-menu-dropdown.supplier-dashboard-user-menu-dropdown-show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.supplier-dashboard-dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #374151;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: background-color 0.2s ease-in-out;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

.supplier-dashboard-dropdown-item:hover {
    background: #f3f4f6;
}

.supplier-dashboard-dropdown-item:first-child {
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.supplier-dashboard-dropdown-item:last-child {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

.supplier-dashboard-dropdown-icon {
    width: 16px;
    height: 16px;
    color: #6b7280;
}

.supplier-dashboard-dropdown-separator {
    height: 1px;
    background: #e5e7eb;
    margin: 0.5rem 0;
}

.supplier-dashboard-logout-form {
    margin: 0;
}

.supplier-dashboard-logout-button {
    color: #dc2626 !important;
}

.supplier-dashboard-logout-button:hover {
    background: #fef2f2 !important;
}

/* ===== MAIN CONTENT ===== */

.supplier-dashboard-main {
    flex: 1;
    margin-left: 0;
    transition: margin-left 0.3s ease-in-out;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.supplier-dashboard-header {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
}

.supplier-dashboard-header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.supplier-dashboard-mobile-menu-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: none;
    background: #f3f4f6;
    border-radius: 8px;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    z-index: 50;
}

.supplier-dashboard-mobile-menu-btn:hover {
    background: #e5e7eb;
    color: #374151;
}

.supplier-dashboard-breadcrumb {
    display: flex;
    align-items: center;
}

.supplier-dashboard-page-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.supplier-dashboard-header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.supplier-dashboard-content {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

/* Header User Dropdown */
.supplier-dashboard-user-dropdown {
    position: relative;
}

.supplier-dashboard-user-dropdown-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border: none;
    background: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.2s ease-in-out;
}

.supplier-dashboard-user-dropdown-btn:hover {
    background: #f3f4f6;
}

.supplier-dashboard-user-avatar-small {
    width: 32px;
    height: 32px;
    background: #3b82f6;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

.supplier-dashboard-user-name-small {
    font-weight: 600;
    font-size: 0.875rem;
    color: #1f2937;
}

.supplier-dashboard-user-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 0.5rem;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease-in-out;
}

.supplier-dashboard-user-dropdown-menu.supplier-dashboard-dropdown-show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.supplier-dashboard-dropdown-divider {
    height: 1px;
    background: #e5e7eb;
    margin: 0.5rem 0;
}

.supplier-dashboard-dropdown-item-danger {
    color: #dc2626 !important;
}

.supplier-dashboard-dropdown-item-danger:hover {
    background: #fef2f2 !important;
}

.supplier-dashboard-dropdown-icon-item {
    width: 16px;
    height: 16px;
    color: #6b7280;
}

/* ===== COMPONENT STYLES ===== */

/* Card Components */
.supplier-dashboard-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    overflow: hidden;
}

.supplier-dashboard-card-header {
    padding: 1.5rem 1.5rem 0 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
}

.supplier-dashboard-card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    flex: 1;
}

.supplier-dashboard-card-content {
    padding: 1.5rem;
}

/* Button Components */
.supplier-dashboard-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    font-size: 0.875rem;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    white-space: nowrap;
    min-height: 44px;
}

.supplier-dashboard-btn-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.8125rem;
    min-height: 36px;
}

.supplier-dashboard-btn-primary {
    background: #3b82f6;
    color: white;
}

.supplier-dashboard-btn-primary:hover {
    background: #2563eb;
}

.supplier-dashboard-btn-secondary {
    background: #6b7280;
    color: white;
}

.supplier-dashboard-btn-secondary:hover {
    background: #4b5563;
}

.supplier-dashboard-btn-danger {
    background: #dc2626;
    color: white;
}

.supplier-dashboard-btn-danger:hover {
    background: #b91c1c;
}

.supplier-dashboard-btn-success {
    background: #16a34a;
    color: white;
}

.supplier-dashboard-btn-success:hover {
    background: #15803d;
}

.supplier-dashboard-btn-outline {
    background: transparent;
    color: #3b82f6;
    border: 1px solid #3b82f6;
}

.supplier-dashboard-btn-outline:hover {
    background: #3b82f6;
    color: white;
}

/* Stats Card */
.supplier-dashboard-stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.supplier-dashboard-stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0.5rem 0;
    line-height: 1;
}

.supplier-dashboard-stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
    margin: 0;
}

.supplier-dashboard-stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.supplier-dashboard-stat-icon.blue {
    background: #dbeafe;
    color: #3b82f6;
}

.supplier-dashboard-stat-icon.green {
    background: #dcfce7;
    color: #16a34a;
}

.supplier-dashboard-stat-icon.purple {
    background: #f3e8ff;
    color: #9333ea;
}

.supplier-dashboard-stat-icon.orange {
    background: #fed7aa;
    color: #ea580c;
}

.supplier-dashboard-stat-icon.yellow {
    background: #fef3c7;
    color: #d97706;
}

.supplier-dashboard-stat-icon.red {
    background: #fecaca;
    color: #dc2626;
}

/* Alert Styles */
.supplier-dashboard-alert {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.supplier-dashboard-alert-success {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.supplier-dashboard-alert-error {
    background: #fef2f2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.supplier-dashboard-alert-warning {
    background: #fefce8;
    color: #a16207;
    border: 1px solid #fef3c7;
}

.supplier-dashboard-alert-info {
    background: #eff6ff;
    color: #1e40af;
    border: 1px solid #dbeafe;
}

.supplier-dashboard-alert-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
}

/* Form Styles */
.supplier-dashboard-form {
    margin: 0;
}

.supplier-dashboard-form-group {
    margin-bottom: 1rem;
}

.supplier-dashboard-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.supplier-dashboard-input,
.supplier-dashboard-select,
.supplier-dashboard-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: white;
}

.supplier-dashboard-input:focus,
.supplier-dashboard-select:focus,
.supplier-dashboard-textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.supplier-dashboard-input-error {
    border-color: #dc2626;
}

.supplier-dashboard-input-error:focus {
    border-color: #dc2626;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.supplier-dashboard-textarea {
    resize: vertical;
    min-height: 80px;
}

/* Table Styles */
.supplier-dashboard-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.supplier-dashboard-table th {
    background: #f8fafc;
    padding: 0.75rem;
    text-align: left;
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
    border-bottom: 1px solid #e5e7eb;
}

.supplier-dashboard-table td {
    padding: 0.75rem;
    border-bottom: 1px solid #f3f4f6;
    color: #6b7280;
    font-size: 0.875rem;
}

.supplier-dashboard-table tr:last-child td {
    border-bottom: none;
}

.supplier-dashboard-table tr:hover {
    background: #f8fafc;
}

/* Badge Styles */
.supplier-dashboard-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 9999px;
}

.supplier-dashboard-badge-success {
    background: #dcfce7;
    color: #166534;
}

.supplier-dashboard-badge-warning {
    background: #fef3c7;
    color: #92400e;
}

.supplier-dashboard-badge-error {
    background: #fee2e2;
    color: #991b1b;
}

.supplier-dashboard-badge-info {
    background: #dbeafe;
    color: #1e40af;
}

/* Empty State */
.supplier-dashboard-empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6b7280;
}

.supplier-dashboard-empty-icon {
    width: 4rem;
    height: 4rem;
    margin: 0 auto 1rem;
    color: #d1d5db;
}

.supplier-dashboard-empty-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

.supplier-dashboard-empty-description {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 1.5rem;
}

/* Loading States */
.supplier-dashboard-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #6b7280;
}

.supplier-dashboard-spinner {
    width: 2rem;
    height: 2rem;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Pagination */
.supplier-dashboard-pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1.5rem;
}

.supplier-dashboard-pagination-btn {
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    background: white;
    color: #6b7280;
    text-decoration: none;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.supplier-dashboard-pagination-btn:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
}

.supplier-dashboard-pagination-btn.active {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.supplier-dashboard-pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Modal Styles */
.supplier-dashboard-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;
}

.supplier-dashboard-modal.active {
    opacity: 1;
    visibility: visible;
}

.supplier-dashboard-modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.95);
    transition: transform 0.3s ease-in-out;
}

.supplier-dashboard-modal.active .supplier-dashboard-modal-content {
    transform: scale(1);
}

.supplier-dashboard-modal-header {
    padding: 1.5rem 1.5rem 0 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 1.5rem;
}

.supplier-dashboard-modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.supplier-dashboard-modal-close {
    width: 32px;
    height: 32px;
    border: none;
    background: #f3f4f6;
    border-radius: 50%;
    color: #6b7280;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease-in-out;
}

.supplier-dashboard-modal-close:hover {
    background: #e5e7eb;
    color: #374151;
}

.supplier-dashboard-modal-body {
    padding: 0 1.5rem;
}

.supplier-dashboard-modal-footer {
    padding: 1.5rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
}

/* ===== MOBILE Z-INDEX FIXES ===== */
/* Note: Comprehensive mobile z-index fixes are in supplier-dashboard-mobile-fixes.css */

/* ===== RESPONSIVE DESIGN ===== */

/* Desktop */
@media (min-width: 1024px) {
    .supplier-dashboard-sidebar {
        position: fixed;
        transform: translateX(0);
    }

    .supplier-dashboard-main {
        margin-left: 280px;
    }

    .supplier-dashboard-mobile-menu-btn {
        display: none;
    }

    .supplier-dashboard-sidebar-overlay {
        display: none;
    }
}

/* Tablet */
@media (max-width: 1023px) and (min-width: 769px) {
    .supplier-dashboard-content {
        padding: 1.5rem;
    }

    .supplier-dashboard-card-content {
        padding: 1rem;
    }

    .supplier-dashboard-stat-card {
        padding: 1rem;
    }

    .supplier-dashboard-stat-value {
        font-size: 1.5rem;
    }

    .supplier-dashboard-stat-icon {
        width: 40px;
        height: 40px;
    }
}

/* Mobile */
@media (max-width: 768px) {
    .supplier-dashboard-content {
        padding: 1rem;
    }

    .supplier-dashboard-header {
        padding: 1rem;
    }

    .supplier-dashboard-card-content {
        padding: 1rem;
    }

    .supplier-dashboard-stat-card {
        padding: 1rem;
    }

    .supplier-dashboard-stat-value {
        font-size: 1.5rem;
    }

    .supplier-dashboard-stat-icon {
        width: 40px;
        height: 40px;
    }

    .supplier-dashboard-user-name-small {
        display: none;
    }
}

/* Small Mobile */
@media (max-width: 480px) {
    .supplier-dashboard-content {
        padding: 0.75rem;
    }

    .supplier-dashboard-page-title {
        font-size: 1.125rem;
    }

    .supplier-dashboard-stat-card {
        padding: 0.75rem;
    }

    .supplier-dashboard-stat-value {
        font-size: 1.25rem;
    }

    .supplier-dashboard-stat-icon {
        width: 36px;
        height: 36px;
    }

    .supplier-dashboard-header-right .supplier-dashboard-user-dropdown {
        display: none;
    }
}

/* ===== PRODUCT CREATION FORM STYLES ===== */

.supplier-dashboard-product-selector {
    position: relative;
}

.supplier-dashboard-new-product-form {
    margin-top: 8px;
    padding: 16px;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    transition: all 0.2s ease-in-out;
}

.supplier-dashboard-new-product-form .flex {
    display: flex;
    align-items: center;
    gap: 8px;
}

.supplier-dashboard-new-product-form .flex-1 {
    flex: 1;
}

.supplier-dashboard-add-new-option {
    color: #3b82f6;
    font-weight: 500;
    background-color: #eff6ff;
}

.supplier-dashboard-add-new-option:hover {
    background-color: #dbeafe;
}

/* Button styles for inline product creation */
.supplier-dashboard-new-product-form .supplier-dashboard-btn {
    min-width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 12px;
}

.supplier-dashboard-new-product-form .supplier-dashboard-btn svg {
    width: 16px;
    height: 16px;
}

/* Animation for form appearance */
.supplier-dashboard-new-product-form[style*="display: block"] {
    animation: supplier-dashboard-slideDown 0.2s ease-out;
}

@keyframes supplier-dashboard-slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading state for save button */
.supplier-dashboard-new-product-form .supplier-dashboard-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.supplier-dashboard-new-product-form .supplier-dashboard-btn:disabled svg {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Mobile responsive adjustments */
@media (max-width: 640px) {
    .supplier-dashboard-new-product-form .flex {
        flex-direction: column;
        gap: 12px;
    }

    .supplier-dashboard-new-product-form .supplier-dashboard-btn {
        width: 100%;
        min-width: auto;
        height: 40px;
    }
}

/* ===== DELIVERY SHOW PAGE STYLES ===== */

/* Status Icons */
.supplier-dashboard-status-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.supplier-dashboard-status-icon.supplier-dashboard-status-pending {
    background-color: #fef3c7;
    color: #d97706;
}

.supplier-dashboard-status-icon.supplier-dashboard-status-received {
    background-color: #d1fae5;
    color: #059669;
}

.supplier-dashboard-status-icon.supplier-dashboard-status-partial {
    background-color: #dbeafe;
    color: #2563eb;
}

.supplier-dashboard-status-icon.supplier-dashboard-status-cancelled {
    background-color: #fee2e2;
    color: #dc2626;
}

/* Status Badges */
.supplier-dashboard-status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.supplier-dashboard-status-badge.supplier-dashboard-status-pending {
    background-color: #fef3c7;
    color: #92400e;
}

.supplier-dashboard-status-badge.supplier-dashboard-status-received {
    background-color: #d1fae5;
    color: #065f46;
}

.supplier-dashboard-status-badge.supplier-dashboard-status-partial {
    background-color: #dbeafe;
    color: #1e40af;
}

.supplier-dashboard-status-badge.supplier-dashboard-status-cancelled {
    background-color: #fee2e2;
    color: #991b1b;
}

/* Progress Bar */
.supplier-dashboard-progress-container {
    min-width: 200px;
}

.supplier-dashboard-progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.supplier-dashboard-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 4px;
    transition: width 0.3s ease-in-out;
}

/* Information Rows */
.supplier-dashboard-info-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 12px 0;
    border-bottom: 1px solid #f3f4f6;
}

.supplier-dashboard-info-row:last-child {
    border-bottom: none;
}

.supplier-dashboard-info-label {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    min-width: 140px;
    flex-shrink: 0;
}

.supplier-dashboard-info-label svg {
    margin-right: 8px;
    flex-shrink: 0;
}

.supplier-dashboard-info-value {
    text-align: right;
    font-size: 0.875rem;
    color: #111827;
    flex: 1;
    margin-left: 16px;
}

/* Notes Content */
.supplier-dashboard-notes-content {
    display: flex;
    align-items: flex-start;
    padding: 16px;
    background-color: #f9fafb;
    border-radius: 8px;
    border-left: 4px solid #3b82f6;
}

/* Mobile responsive adjustments for show page */
@media (max-width: 768px) {
    .supplier-dashboard-progress-container {
        min-width: auto;
        width: 100%;
        margin-top: 16px;
    }

    .supplier-dashboard-info-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .supplier-dashboard-info-label {
        min-width: auto;
    }

    .supplier-dashboard-info-value {
        text-align: left;
        margin-left: 0;
    }

    .supplier-dashboard-notes-content {
        flex-direction: column;
        gap: 12px;
    }

    .supplier-dashboard-notes-content svg {
        margin-right: 0;
        margin-bottom: 8px;
    }
}

/* ===== RETURNS PAGE SPECIFIC STYLES ===== */

/* Returns Action Buttons */
.supplier-dashboard-returns-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 140px;
}

.supplier-dashboard-returns-actions .supplier-dashboard-btn {
    justify-content: center;
    font-size: 0.75rem;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
    font-weight: 500;
}

.supplier-dashboard-returns-actions .supplier-dashboard-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.supplier-dashboard-returns-actions .supplier-dashboard-btn svg {
    flex-shrink: 0;
}

/* Info Card for Returns Header */
.supplier-dashboard-info-card {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 12px 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Enhanced Table Styling for Returns */
.supplier-dashboard-card table tbody tr:hover {
    background-color: #f8fafc;
    transition: background-color 0.2s ease;
}

.supplier-dashboard-card table td {
    vertical-align: top;
    padding: 16px 24px;
}

/* Mobile Responsive for Returns Actions */
@media (max-width: 768px) {
    .supplier-dashboard-returns-actions {
        flex-direction: row;
        flex-wrap: wrap;
        min-width: auto;
        gap: 6px;
    }

    .supplier-dashboard-returns-actions .supplier-dashboard-btn {
        flex: 1;
        min-width: 100px;
        font-size: 0.7rem;
        padding: 6px 8px;
    }

    .supplier-dashboard-info-card {
        padding: 8px 12px;
    }

    .supplier-dashboard-info-card .text-sm {
        font-size: 0.75rem;
    }
}
