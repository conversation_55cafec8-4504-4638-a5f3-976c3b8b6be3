@extends('layouts.admin')

@section('title', 'Riwayat Retur')

@section('content')
<div class="space-y-6">
    <!-- Header with Actions -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Riwayat Retur</h1>
                    <p class="text-gray-600 mt-1">Riwayat semua retur yang telah diproses</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="{{ route('admin.return-history.export', request()->query()) }}" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Export CSV
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <div class="admin-dashboard-stat-card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="admin-dashboard-stat-icon bg-blue-100 text-blue-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="admin-dashboard-stat-value">{{ number_format($stats['total_processed']) }}</p>
                    <p class="admin-dashboard-stat-label">Total Diproses</p>
                </div>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="admin-dashboard-stat-icon bg-red-100 text-red-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="admin-dashboard-stat-value">{{ number_format($stats['deleted']) }}</p>
                    <p class="admin-dashboard-stat-label">Dihapus</p>
                </div>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="admin-dashboard-stat-icon bg-yellow-100 text-yellow-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="admin-dashboard-stat-value">{{ number_format($stats['resent']) }}</p>
                    <p class="admin-dashboard-stat-label">Dikirim Ulang</p>
                </div>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="admin-dashboard-stat-icon bg-green-100 text-green-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="admin-dashboard-stat-value">{{ number_format($stats['accepted']) }}</p>
                    <p class="admin-dashboard-stat-label">Diterima</p>
                </div>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="admin-dashboard-stat-icon bg-gray-100 text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="admin-dashboard-stat-value">{{ number_format($stats['rejected_final']) }}</p>
                    <p class="admin-dashboard-stat-label">Ditolak Final</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <form method="GET" action="{{ route('admin.return-history.index') }}" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <!-- Month Filter -->
                    <div>
                        <label for="month" class="block text-sm font-medium text-gray-700 mb-2">Bulan</label>
                        <select name="month" id="month" class="admin-dashboard-select">
                            @foreach($availableMonths as $monthOption)
                                <option value="{{ $monthOption['value'] }}" {{ $filterMonth === $monthOption['value'] ? 'selected' : '' }}>
                                    {{ $monthOption['label'] }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Return Type Filter -->
                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Jenis Retur</label>
                        <select name="type" id="type" class="admin-dashboard-select">
                            <option value="all" {{ request('type', 'all') === 'all' ? 'selected' : '' }}>Semua Retur</option>
                            <option value="store" {{ request('type') === 'store' ? 'selected' : '' }}>Retur dari Toko</option>
                            <option value="supplier" {{ request('type') === 'supplier' ? 'selected' : '' }}>Retur ke Supplier</option>
                        </select>
                    </div>

                    <!-- Search -->
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Cari</label>
                        <input type="text" name="search" id="search" value="{{ request('search') }}" 
                               placeholder="Cari produk, toko, supplier..." class="admin-dashboard-input">
                    </div>

                    <!-- Filter Button -->
                    <div class="flex items-end">
                        <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary w-full">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                            </svg>
                            Filter
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Returns History Table -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            @if($returns->count() > 0)
            <div class="overflow-x-auto">
                <table class="admin-dashboard-table">
                    <thead>
                        <tr>
                            <th>Tanggal Retur</th>
                            <th>Tanggal Diproses</th>
                            <th>Produk</th>
                            <th>Asal → Tujuan</th>
                            <th>Jumlah</th>
                            <th>Alasan</th>
                            <th>Aksi Pemrosesan</th>
                            <th>Diproses Oleh</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($returns as $return)
                        <tr>
                            <td>
                                <div class="font-medium text-gray-900">
                                    {{ $return->return_date->format('d/m/Y') }}
                                </div>
                            </td>
                            <td>
                                <div class="font-medium text-gray-900">
                                    {{ $return->processed_date ? $return->processed_date->format('d/m/Y') : '-' }}
                                </div>
                            </td>
                            <td>
                                <div class="font-medium text-gray-900">{{ $return->product->name }}</div>
                                <div class="text-sm text-gray-500">{{ $return->product->sku }}</div>
                            </td>
                            <td>
                                <div class="text-sm">
                                    <span class="font-medium">{{ $return->source }}</span>
                                    <span class="text-gray-400 mx-1">→</span>
                                    <span class="font-medium">{{ $return->destination }}</span>
                                </div>
                            </td>
                            <td>
                                <span class="admin-dashboard-badge admin-dashboard-badge-blue">
                                    {{ number_format($return->quantity) }} unit
                                </span>
                            </td>
                            <td>
                                <span class="admin-dashboard-badge admin-dashboard-badge-gray">
                                    {{ $return->reason_in_indonesian }}
                                </span>
                            </td>
                            <td>
                                @if($return->processing_action)
                                    <span class="admin-dashboard-badge 
                                        @if($return->processing_action === 'accepted') admin-dashboard-badge-green
                                        @elseif($return->processing_action === 'deleted') admin-dashboard-badge-red
                                        @elseif($return->processing_action === 'resent') admin-dashboard-badge-yellow
                                        @else admin-dashboard-badge-gray
                                        @endif">
                                        {{ $return->processing_action_in_indonesian }}
                                    </span>
                                @else
                                    <span class="text-gray-400">-</span>
                                @endif
                            </td>
                            <td>
                                <div class="text-sm text-gray-900">
                                    {{ $return->processedBy->name ?? '-' }}
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-6">
                {{ $returns->links() }}
            </div>
            @else
            <div class="text-center py-12">
                <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Tidak Ada Riwayat Retur</h3>
                <p class="text-gray-500">Belum ada retur yang diproses untuk periode yang dipilih.</p>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
