<?php

namespace App\Http\Controllers\Supplier;

use App\Http\Controllers\Controller;
use App\Models\ReturnModel;
use App\Models\SupplierDelivery;
use App\Traits\SupplierHelper;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Carbon\Carbon;

class SupplierReturnHistoryController extends Controller
{
    use SupplierHelper;

    /**
     * Display return history for the current supplier.
     */
    public function index(Request $request)
    {
        // Get current supplier using the helper trait
        $supplier = $this->getCurrentSupplier();
        
        if (!$supplier) {
            return redirect()->route('supplier.dashboard')
                ->with('error', 'Akses tidak diizinkan');
        }

        // Get filter parameters
        $filterMonth = $request->get('month', now()->format('Y-m'));
        $search = $request->get('search');

        // Parse month filter
        $monthDate = Carbon::createFromFormat('Y-m', $filterMonth);
        $startDate = $monthDate->startOfMonth()->toDateString();
        $endDate = $monthDate->endOfMonth()->toDateString();

        // Build query for processed returns
        $query = ReturnModel::with(['product', 'store', 'supplier', 'requestedBy', 'approvedBy', 'processedBy'])
            ->processed()
            ->where('supplier_id', $supplier->id)
            ->whereBetween('processed_date', [$startDate, $endDate]);

        // Apply search filter
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->whereHas('product', function ($productQuery) use ($search) {
                    $productQuery->where('name', 'like', "%{$search}%")
                        ->orWhere('sku', 'like', "%{$search}%");
                })
                ->orWhereHas('store', function ($storeQuery) use ($search) {
                    $storeQuery->where('name', 'like', "%{$search}%");
                })
                ->orWhere('reason', 'like', "%{$search}%")
                ->orWhere('processing_notes', 'like', "%{$search}%");
            });
        }

        // Get paginated results
        $returns = $query->orderBy('processed_date', 'desc')->paginate(15);

        // Calculate statistics
        $stats = [
            'total_processed' => ReturnModel::processed()
                ->where('supplier_id', $supplier->id)
                ->whereBetween('processed_date', [$startDate, $endDate])
                ->count(),
            'accepted' => ReturnModel::processed()
                ->where('supplier_id', $supplier->id)
                ->where('processing_action', 'accepted')
                ->whereBetween('processed_date', [$startDate, $endDate])
                ->count(),
            'resent' => ReturnModel::processed()
                ->where('supplier_id', $supplier->id)
                ->where('processing_action', 'resent')
                ->whereBetween('processed_date', [$startDate, $endDate])
                ->count(),
            'rejected_final' => ReturnModel::processed()
                ->where('supplier_id', $supplier->id)
                ->where('processing_action', 'rejected_final')
                ->whereBetween('processed_date', [$startDate, $endDate])
                ->count(),
        ];

        // Generate available months for filter dropdown
        $availableMonths = $this->getAvailableMonths($supplier->id);

        return view('supplier.return-history.index', compact(
            'returns',
            'stats',
            'filterMonth',
            'availableMonths',
            'supplier'
        ));
    }

    /**
     * Export return history to CSV.
     */
    public function export(Request $request)
    {
        // Get current supplier using the helper trait
        $supplier = $this->getCurrentSupplier();
        
        if (!$supplier) {
            return redirect()->route('supplier.dashboard')
                ->with('error', 'Akses tidak diizinkan');
        }

        // Get filter parameters
        $filterMonth = $request->get('month', now()->format('Y-m'));
        $search = $request->get('search');

        // Parse month filter
        $monthDate = Carbon::createFromFormat('Y-m', $filterMonth);
        $startDate = $monthDate->startOfMonth()->toDateString();
        $endDate = $monthDate->endOfMonth()->toDateString();

        // Build query for processed returns
        $query = ReturnModel::with(['product', 'store', 'supplier', 'requestedBy', 'approvedBy', 'processedBy'])
            ->processed()
            ->where('supplier_id', $supplier->id)
            ->whereBetween('processed_date', [$startDate, $endDate]);

        // Apply search filter
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->whereHas('product', function ($productQuery) use ($search) {
                    $productQuery->where('name', 'like', "%{$search}%")
                        ->orWhere('sku', 'like', "%{$search}%");
                })
                ->orWhereHas('store', function ($storeQuery) use ($search) {
                    $storeQuery->where('name', 'like', "%{$search}%");
                })
                ->orWhere('reason', 'like', "%{$search}%")
                ->orWhere('processing_notes', 'like', "%{$search}%");
            });
        }

        // Get all results for export
        $returns = $query->orderBy('processed_date', 'desc')->get();

        // Prepare CSV data
        $csvData = [];

        // Add header with company info and export details
        $csvData[] = ['RIWAYAT RETUR PRODUK - INDAH BERKAH ABADI'];
        $csvData[] = ['Supplier: ' . $supplier->name];
        $csvData[] = ['Periode: ' . $monthDate->format('F Y')];
        $csvData[] = ['Diekspor pada: ' . now()->format('d/m/Y H:i:s')];
        $csvData[] = ['Jenis Data: Retur Produk yang Telah Diproses'];
        $csvData[] = []; // Empty row for separation

        // Add column headers
        $csvData[] = [
            'Tanggal Retur',
            'Tanggal Diproses',
            'Jenis Retur',
            'Produk',
            'SKU',
            'Asal',
            'Jumlah',
            'Alasan',
            'Aksi Pemrosesan',
            'Catatan Pemrosesan',
            'Diproses Oleh'
        ];

        foreach ($returns as $return) {
            // Determine return type based on store_id
            $returnType = $return->store_id ? 'Retur dari Toko' : 'Retur dari Gudang';
            $origin = $return->store->name ?? 'Gudang Pusat';

            $csvData[] = [
                $return->return_date ? $return->return_date->format('d/m/Y') : '',
                $return->processed_date ? $return->processed_date->format('d/m/Y') : '',
                $returnType,
                $return->product->name ?? '',
                $return->product->sku ?? '',
                $origin,
                $return->quantity,
                $return->reason_in_indonesian,
                $return->processing_action_in_indonesian ?? '',
                $return->processing_notes ?? '',
                $return->processedBy->name ?? ''
            ];
        }

        // Generate filename
        $filename = 'riwayat_retur_supplier_' . $supplier->name . '_' . $filterMonth . '.csv';
        $filename = str_replace([' ', '/'], ['_', '_'], $filename);

        // Create CSV response
        $response = new Response();
        $response->headers->set('Content-Type', 'text/csv; charset=UTF-8');
        $response->headers->set('Content-Disposition', 'attachment; filename="' . $filename . '"');

        // Add BOM for UTF-8
        $csvContent = "\xEF\xBB\xBF";
        
        foreach ($csvData as $row) {
            $csvContent .= implode(',', array_map(function ($field) {
                return '"' . str_replace('"', '""', $field) . '"';
            }, $row)) . "\n";
        }

        $response->setContent($csvContent);
        return $response;
    }

    /**
     * Get available months for filter dropdown.
     */
    private function getAvailableMonths($supplierId)
    {
        $months = [];
        
        // Get the earliest processed return date for this supplier
        $earliestReturn = ReturnModel::processed()
            ->where('supplier_id', $supplierId)
            ->orderBy('processed_date', 'asc')
            ->first();

        $startDate = $earliestReturn ? Carbon::parse($earliestReturn->processed_date) : now();
        $endDate = now();

        // Generate months from earliest to current
        $current = $startDate->copy()->startOfMonth();
        while ($current->lte($endDate)) {
            $months[] = [
                'value' => $current->format('Y-m'),
                'label' => $current->format('F Y')
            ];
            $current->addMonth();
        }

        return array_reverse($months); // Show newest first
    }
}
