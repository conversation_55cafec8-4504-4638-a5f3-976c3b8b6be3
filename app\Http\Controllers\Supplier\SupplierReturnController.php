<?php

namespace App\Http\Controllers\Supplier;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ReturnModel;
use App\Models\SupplierDelivery;
use App\Models\Supplier;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use App\Traits\SupplierHelper;

class SupplierReturnController extends Controller
{
    use SupplierHelper;
    /**
     * Display a listing of returns and cancelled deliveries.
     */
    public function index(Request $request)
    {
        // Get current supplier using the helper trait
        $supplier = $this->getCurrentSupplier();

        // Get current month for default filtering
        $currentMonth = Carbon::now()->format('Y-m');
        $filterMonth = $request->get('month', $currentMonth);

        // Parse the filter month
        $startDate = Carbon::createFromFormat('Y-m', $filterMonth)->startOfMonth();
        $endDate = Carbon::createFromFormat('Y-m', $filterMonth)->endOfMonth();

        // Get regular returns (only show active returns - not moved to history)
        $returnsQuery = ReturnModel::with(['product', 'store', 'supplier', 'requestedBy', 'approvedBy']);
        $returnsQuery->whereBetween('return_date', [$startDate, $endDate]);
        $returnsQuery->where('supplier_id', $supplier->id);
        $returnsQuery->active(); // Only show active returns (not moved to history)

        // Get cancelled deliveries for this supplier
        $cancelledDeliveriesQuery = SupplierDelivery::with(['product', 'supplier', 'receivedBy']);
        $cancelledDeliveriesQuery->whereBetween('delivery_date', [$startDate, $endDate]);
        $cancelledDeliveriesQuery->where('supplier_id', $supplier->id);
        $cancelledDeliveriesQuery->where('status', 'cancelled');
        
        // Apply search filter if provided
        if ($request->filled('search')) {
            $searchTerm = $request->get('search');
            $returnsQuery->where(function ($q) use ($searchTerm) {
                $q->whereHas('product', function ($productQuery) use ($searchTerm) {
                    $productQuery->where('name', 'like', "%{$searchTerm}%");
                })
                ->orWhereHas('store', function ($storeQuery) use ($searchTerm) {
                    $storeQuery->where('name', 'like', "%{$searchTerm}%");
                });
            });

            $cancelledDeliveriesQuery->where(function ($q) use ($searchTerm) {
                $q->whereHas('product', function ($productQuery) use ($searchTerm) {
                    $productQuery->where('name', 'like', "%{$searchTerm}%");
                });
            });
        }

        // Apply status filter if provided
        if ($request->filled('status')) {
            $returnsQuery->where('status', $request->get('status'));
        }

        // Apply reason filter if provided
        if ($request->filled('reason')) {
            $returnsQuery->where('reason', $request->get('reason'));
        }

        // Sort by newest first (descending order by date)
        $returns = $returnsQuery->orderBy('return_date', 'desc')->paginate(15);
        $cancelledDeliveries = $cancelledDeliveriesQuery->orderBy('delivery_date', 'desc')->paginate(15);

        // Get statistics
        $stats = [
            'total_returns' => ReturnModel::whereBetween('return_date', [$startDate, $endDate])
                ->where('supplier_id', $supplier->id)->count(),
            'requested_returns' => ReturnModel::whereBetween('return_date', [$startDate, $endDate])
                ->where('supplier_id', $supplier->id)->where('status', 'requested')->count(),
            'approved_returns' => ReturnModel::whereBetween('return_date', [$startDate, $endDate])
                ->where('supplier_id', $supplier->id)->where('status', 'approved')->count(),
            'completed_returns' => ReturnModel::whereBetween('return_date', [$startDate, $endDate])
                ->where('supplier_id', $supplier->id)->where('status', 'completed')->count(),
            'cancelled_deliveries' => SupplierDelivery::whereBetween('delivery_date', [$startDate, $endDate])
                ->where('supplier_id', $supplier->id)->where('status', 'cancelled')->count(),
        ];

        return view('supplier.returns.index', compact('returns', 'cancelledDeliveries', 'stats', 'filterMonth'));
    }
    
    /**
     * Display the specified return.
     */
    public function show(ReturnModel $return)
    {
        $return->load(['product', 'store', 'supplier', 'requestedBy', 'approvedBy']);
        
        return view('supplier.returns.show', compact('return'));
    }
    
    /**
     * Respond to a return request.
     */
    public function respond(Request $request, ReturnModel $return)
    {
        // Only allow response if return is approved and involves a supplier
        if ($return->status !== 'approved' || is_null($return->supplier_id)) {
            return redirect()->route('supplier.returns.index')
                ->with('error', 'Return ini tidak dapat direspons');
        }
        
        $validatedData = $request->validate([
            'action' => 'required|in:accept,reject',
            'supplier_notes' => 'nullable|string|max:1000',
        ], [
            'action.required' => 'Aksi wajib dipilih',
            'action.in' => 'Aksi tidak valid',
            'supplier_notes.max' => 'Catatan maksimal 1000 karakter',
        ]);
        
        if ($validatedData['action'] === 'accept') {
            $return->update([
                'status' => 'in_transit',
                'admin_notes' => ($return->admin_notes ?? '') . "\n\nSupplier Response: " . ($validatedData['supplier_notes'] ?? 'Diterima'),
            ]);
            
            $message = 'Return berhasil diterima dan sedang dalam perjalanan';
        } else {
            $return->update([
                'status' => 'rejected',
                'admin_notes' => ($return->admin_notes ?? '') . "\n\nSupplier Response: " . ($validatedData['supplier_notes'] ?? 'Ditolak'),
            ]);
            
            $message = 'Return berhasil ditolak';
        }
        
        return redirect()->route('supplier.returns.index')
            ->with('success', $message);
    }

    /**
     * Delete a cancelled delivery.
     */
    public function deleteCancelledDelivery(Request $request, SupplierDelivery $delivery)
    {
        // Get current supplier using the helper trait
        $supplier = $this->getCurrentSupplier();

        // Verify this delivery belongs to the current supplier and is cancelled
        if (!$supplier || $delivery->supplier_id !== $supplier->id || $delivery->status !== 'cancelled') {
            return redirect()->route('supplier.returns.index')
                ->with('error', 'Pengiriman tidak dapat dihapus');
        }

        $productName = $delivery->product->name;

        DB::transaction(function () use ($delivery, $supplier) {
            // Auto-approve any related warehouse returns for the same product from this supplier
            $this->autoApproveRelatedWarehouseReturns($delivery->product_id, $supplier->id, 'Supplier menghapus pengiriman yang dibatalkan');

            $delivery->delete();
        });

        return redirect()->route('supplier.returns.index')
            ->with('success', "Pengiriman yang dibatalkan untuk produk '{$productName}' berhasil dihapus");
    }

    /**
     * Resend a cancelled delivery (create new delivery based on cancelled one).
     */
    public function resendCancelledDelivery(Request $request, SupplierDelivery $delivery)
    {
        // Get current supplier using the helper trait
        $supplier = $this->getCurrentSupplier();

        // Verify this delivery belongs to the current supplier and is cancelled
        if (!$supplier || $delivery->supplier_id !== $supplier->id || $delivery->status !== 'cancelled') {
            return redirect()->route('supplier.returns.index')
                ->with('error', 'Pengiriman tidak dapat dikirim ulang');
        }

        $validatedData = $request->validate([
            'delivery_date' => 'required|date|after_or_equal:today',
            'notes' => 'nullable|string|max:1000',
        ], [
            'delivery_date.required' => 'Tanggal pengiriman wajib diisi',
            'delivery_date.date' => 'Format tanggal tidak valid',
            'delivery_date.after_or_equal' => 'Tanggal pengiriman tidak boleh kurang dari hari ini',
            'notes.max' => 'Catatan maksimal 1000 karakter',
        ]);

        $productName = $delivery->product->name;

        DB::transaction(function () use ($delivery, $validatedData, $supplier) {
            // Create new delivery based on the cancelled one
            SupplierDelivery::create([
                'supplier_id' => $supplier->id,
                'product_id' => $delivery->product_id,
                'quantity' => $delivery->quantity,
                'unit_price' => $delivery->unit_price,
                'total_price' => $delivery->total_price,
                'delivery_date' => $validatedData['delivery_date'],
                'status' => 'pending',
                'notes' => $validatedData['notes'] ?? 'Pengiriman ulang dari pengiriman yang dibatalkan',
            ]);

            // Auto-approve any related warehouse returns for the same product from this supplier
            $this->autoApproveRelatedWarehouseReturns($delivery->product_id, $supplier->id, 'Supplier mengirim ulang produk');

            // Delete the original cancelled delivery after creating the replacement
            $delivery->delete();
        });

        return redirect()->route('supplier.returns.index')
            ->with('success', "Pengiriman ulang untuk produk '{$productName}' berhasil dibuat");
    }

    /**
     * Approve a return request (simple approval action).
     */
    public function approve(Request $request, ReturnModel $return)
    {
        // Get current supplier using the helper trait
        $supplier = $this->getCurrentSupplier();

        // Verify this return belongs to the current supplier
        if (!$supplier || $return->supplier_id !== $supplier->id) {
            return redirect()->route('supplier.returns.index')
                ->with('error', 'Retur tidak dapat disetujui');
        }

        // Only allow approving requested returns
        if ($return->status !== 'requested') {
            return redirect()->route('supplier.returns.index')
                ->with('error', 'Retur ini sudah diproses sebelumnya');
        }

        $validatedData = $request->validate([
            'notes' => 'nullable|string|max:1000',
        ], [
            'notes.max' => 'Catatan maksimal 1000 karakter',
        ]);

        $productName = $return->product->name;

        DB::transaction(function () use ($return, $supplier, $validatedData) {
            // Mark as completed and move to history
            $return->update([
                'status' => 'completed',
                'completed_date' => now(),
                'admin_notes' => $validatedData['notes'] ?? 'Retur disetujui oleh supplier',
            ]);

            // Auto-approve any related warehouse returns for the same product from this supplier
            $this->autoApproveRelatedWarehouseReturns($return->product_id, $supplier->id, 'Supplier menyetujui retur produk');

            // Move to history instead of deleting
            $return->moveToHistory('accepted', 'Retur disetujui oleh supplier', auth()->id());
        });

        return redirect()->route('supplier.returns.index')
            ->with('success', "Retur untuk produk '{$productName}' telah disetujui dan dipindahkan ke riwayat");
    }

    /**
     * Auto-approve related warehouse returns when supplier takes action
     */
    private function autoApproveRelatedWarehouseReturns($productId, $supplierId, $reason)
    {
        // Find pending warehouse returns for the same product and supplier
        $warehouseReturns = ReturnModel::where('product_id', $productId)
            ->where('supplier_id', $supplierId)
            ->where('store_id', null) // Warehouse returns only
            ->where('status', 'requested')
            ->get();

        foreach ($warehouseReturns as $warehouseReturn) {
            $warehouseReturn->update([
                'status' => 'approved',
                'approved_date' => now(),
                'approved_by' => auth()->id(),
                'admin_notes' => $reason . ' - Otomatis disetujui karena supplier mengambil tindakan',
            ]);
        }

        if ($warehouseReturns->count() > 0) {
            \Log::info("Auto-approved {$warehouseReturns->count()} warehouse returns for product {$productId} from supplier {$supplierId}. Reason: {$reason}");
        }
    }
}
